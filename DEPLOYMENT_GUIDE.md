# Phishing URL Detection - Deployment Guide

## 🚀 Quick Start

### Prerequisites
Ensure you have Python 3.8+ and the following packages installed:

```bash
pip install fastapi uvicorn pandas numpy scikit-learn requests beautifulsoup4 category-encoders joblib
```

### Running the Application

1. **Start the FastAPI server:**
   ```bash
   python app.py
   ```
   
   The server will start on `http://localhost:8000`

2. **Access the application:**
   - **Web Interface**: http://localhost:8000
   - **API Documentation**: http://localhost:8000/docs
   - **Health Check**: http://localhost:8000/health

## 📁 File Structure

```
├── app.py                    # Main FastAPI application
├── feature_extractor.py     # URL feature extraction module
├── preprocessor.py          # Data preprocessing pipeline
├── test_app.py             # Comprehensive testing script
├── stacking_ensemble.joblib # Trained ML model
├── README.md               # Detailed documentation
└── DEPLOYMENT_GUIDE.md     # This file
```

## 🔧 Core Components

### 1. Feature Extractor (`feature_extractor.py`)
- Extracts exactly 30 features from URLs
- Fetches actual web content for analysis
- Handles network errors and timeouts gracefully
- Returns features in the exact order expected by the model

### 2. Preprocessor (`preprocessor.py`)
- Applies the exact preprocessing pipeline from training
- Missing value imputation (median for numeric, most frequent for categorical)
- Target encoding for TLD feature
- Standard scaling for all features

### 3. FastAPI Application (`app.py`)
- REST API endpoints for URL prediction
- Embedded web interface
- Comprehensive error handling
- Health monitoring

## 🌐 API Usage

### Single URL Prediction
```bash
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://example.com"}'
```

**Response:**
```json
{
    "url": "https://example.com",
    "prediction": 0,
    "prediction_label": "Legitimate",
    "confidence": 0.95,
    "probabilities": {
        "legitimate": 0.95,
        "phishing": 0.05
    },
    "processing_time": 1.23,
    "status": "success"
}
```

### Batch Prediction
```bash
curl -X POST "http://localhost:8000/predict/batch" \
     -H "Content-Type: application/json" \
     -d '["https://example1.com", "https://example2.com"]'
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
python test_app.py
```

This will test:
- Feature extraction functionality
- Preprocessing pipeline
- Model predictions
- API endpoints (if server is running)

## 🔍 Features Extracted

The system extracts these 30 features in exact order:

1. **LengthOfURL** - Total URL length
2. **URLComplexity** - Complexity score
3. **TLD** - Top-level domain (encoded)
4. **LetterCntInURL** - Letter count
5. **URLLetterRatio** - Letter ratio
6. **DigitCntInURL** - Digit count
7. **URLDigitRatio** - Digit ratio
8. **OtherSpclCharCntInURL** - Special character count
9. **HavingPath** - Has path component
10. **PathLength** - Path length
11. **HasSSL** - Uses HTTPS
12. **LineOfCode** - HTML line count
13. **LongestLineLength** - Longest HTML line
14. **HasFavicon** - Has favicon
15. **HasRobotsBlocked** - Robots.txt blocks
16. **IsSelfRedirects** - Redirect count
17. **HasDescription** - Has meta description
18. **HasSubmitButton** - Has forms/buttons
19. **HasCopyrightInfoKey** - Has copyright info
20. **CntImages** - Image count
21. **CntFilesCSS** - CSS file count
22. **CntFilesJS** - JavaScript file count
23. **CntSelfHRef** - Internal link count
24. **CntEmptyRef** - Empty reference count
25. **CntExternalRef** - External reference count
26. **CntIFrame** - Iframe count
27. **UniqueFeatureCnt** - Unique HTML feature count
28. **ShannonEntropy** - URL entropy
29. **KolmogorovComplexity** - Complexity estimate
30. **LikelinessIndex** - Composite score

## ⚙️ Configuration

### Timeout Settings
Modify in `feature_extractor.py`:
```python
extractor = URLFeatureExtractor(timeout=10, max_retries=2)
```

### Server Settings
Modify in `app.py`:
```python
uvicorn.run("app:app", host="0.0.0.0", port=8000)
```

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   pip install --upgrade fastapi uvicorn pandas numpy scikit-learn requests beautifulsoup4 category-encoders joblib
   ```

2. **Model File Not Found**
   - Ensure `stacking_ensemble.joblib` is in the same directory as the Python files

3. **Network Timeouts**
   - Some URLs may be slow; this is handled gracefully with default values

4. **Feature Mismatch**
   - The model expects exact feature names including "HasCopyrightInfoKey " (with trailing space)

### Debug Mode
Run with debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔒 Security Considerations

- The application fetches content from external URLs
- Proper timeout limits prevent hanging requests
- Input validation prevents malicious input
- No sensitive data is logged or stored

## 📊 Performance

- **Feature Extraction**: ~1-3 seconds per URL
- **Preprocessing**: ~0.01 seconds
- **Prediction**: ~0.1 seconds
- **Total**: ~1-4 seconds per URL

## 🚀 Production Deployment

### Using Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app:app --bind 0.0.0.0:8000
```

### Using Docker
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Environment Variables
```bash
export PHISHING_MODEL_PATH="/path/to/stacking_ensemble.joblib"
export PHISHING_TIMEOUT=10
export PHISHING_MAX_RETRIES=2
```

## 📈 Monitoring

### Health Check
```bash
curl http://localhost:8000/health
```

### Metrics
The application provides:
- Processing time per request
- Success/failure rates
- Feature extraction statistics

## 🔄 Updates

To update the model:
1. Replace `stacking_ensemble.joblib` with new model
2. Ensure preprocessing pipeline matches
3. Restart the application

## 📞 Support

For issues or questions:
1. Check the logs for error details
2. Run the test suite to identify problems
3. Verify all dependencies are installed
4. Ensure the model file is accessible

## 🎯 Success Criteria

The application successfully:
- ✅ Extracts 30 features from URLs
- ✅ Applies exact preprocessing pipeline
- ✅ Makes accurate predictions
- ✅ Provides REST API and web interface
- ✅ Handles errors gracefully
- ✅ Processes real URLs from the internet
