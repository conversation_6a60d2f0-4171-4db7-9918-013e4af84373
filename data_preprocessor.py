import pandas as pd
import numpy as np
import joblib
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler
from category_encoders import TargetEncoder
import warnings

warnings.filterwarnings('ignore')

class DataPreprocessor:
    """
    Data preprocessing pipeline that matches the exact preprocessing steps
    from the training code in stacking.py (excluding feature selection since
    the 30 features are already pre-selected for the saved model).
    """
    
    def __init__(self):
        """Initialize the preprocessor."""
        self.num_imputer = None
        self.cat_imputer = None
        self.target_encoder = None
        self.scaler = None
        self.categorical_cols = []
        self.numerical_cols = []
        self.is_fitted = False
    
    def fit(self, X, y=None):
        """
        Fit the preprocessing pipeline on training data.
        
        Args:
            X (pd.DataFrame): Feature data
            y (pd.Series, optional): Target data (needed for target encoding)
        """
        # Identify categorical and numerical columns
        self.categorical_cols = X.select_dtypes(include=['object', 'category']).columns.tolist()
        self.numerical_cols = X.select_dtypes(include=[np.number]).columns.tolist()
        
        # 1. Handle Missing Values
        # Impute numerical features with median
        if self.numerical_cols:
            self.num_imputer = SimpleImputer(strategy='median')
            self.num_imputer.fit(X[self.numerical_cols])
        
        # Impute categorical features with most frequent
        if self.categorical_cols:
            self.cat_imputer = SimpleImputer(strategy='most_frequent')
            self.cat_imputer.fit(X[self.categorical_cols])
        
        # 2. Target Encoding for Categorical Features
        if self.categorical_cols and y is not None:
            self.target_encoder = TargetEncoder(cols=self.categorical_cols, smoothing=1.0)
            # Apply imputation first, then fit target encoder
            X_imputed = X.copy()
            if self.num_imputer:
                X_imputed[self.numerical_cols] = self.num_imputer.transform(X_imputed[self.numerical_cols])
            if self.cat_imputer:
                X_imputed[self.categorical_cols] = self.cat_imputer.transform(X_imputed[self.categorical_cols])
            
            self.target_encoder.fit(X_imputed, y)
        
        # 3. Scaling
        # Apply all previous transformations to get the data ready for scaling
        X_processed = self.transform_without_scaling(X, y)
        self.scaler = StandardScaler()
        self.scaler.fit(X_processed)
        
        self.is_fitted = True
        return self
    
    def transform(self, X, y=None):
        """
        Transform the data using the fitted preprocessing pipeline.
        
        Args:
            X (pd.DataFrame): Feature data to transform
            y (pd.Series, optional): Target data (not used in transform)
            
        Returns:
            pd.DataFrame: Preprocessed data
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform")
        
        X_processed = X.copy()
        
        # 1. Handle Missing Values
        if self.num_imputer and self.numerical_cols:
            X_processed[self.numerical_cols] = self.num_imputer.transform(X_processed[self.numerical_cols])
        
        if self.cat_imputer and self.categorical_cols:
            X_processed[self.categorical_cols] = self.cat_imputer.transform(X_processed[self.categorical_cols])
        
        # 2. Target Encoding
        if self.target_encoder and self.categorical_cols:
            X_processed = self.target_encoder.transform(X_processed)
        
        # 3. Scaling
        X_scaled = self.scaler.transform(X_processed)
        
        # Convert back to DataFrame with original column names
        result_df = pd.DataFrame(X_scaled, columns=X_processed.columns, index=X.index)
        return result_df
    
    def transform_without_scaling(self, X, y=None):
        """
        Transform data without scaling (used internally for fitting scaler).
        
        Args:
            X (pd.DataFrame): Feature data to transform
            y (pd.Series, optional): Target data (not used)
            
        Returns:
            pd.DataFrame: Preprocessed data without scaling
        """
        X_processed = X.copy()
        
        # 1. Handle Missing Values
        if self.num_imputer and self.numerical_cols:
            X_processed[self.numerical_cols] = self.num_imputer.transform(X_processed[self.numerical_cols])
        
        if self.cat_imputer and self.categorical_cols:
            X_processed[self.categorical_cols] = self.cat_imputer.transform(X_processed[self.categorical_cols])
        
        # 2. Target Encoding
        if self.target_encoder and self.categorical_cols:
            X_processed = self.target_encoder.transform(X_processed)
        
        return X_processed
    
    def fit_transform(self, X, y=None):
        """
        Fit the preprocessor and transform the data in one step.
        
        Args:
            X (pd.DataFrame): Feature data
            y (pd.Series, optional): Target data
            
        Returns:
            pd.DataFrame: Preprocessed data
        """
        return self.fit(X, y).transform(X, y)
    
    def save(self, filepath):
        """
        Save the fitted preprocessor to a file.
        
        Args:
            filepath (str): Path to save the preprocessor
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before saving")
        
        preprocessor_data = {
            'num_imputer': self.num_imputer,
            'cat_imputer': self.cat_imputer,
            'target_encoder': self.target_encoder,
            'scaler': self.scaler,
            'categorical_cols': self.categorical_cols,
            'numerical_cols': self.numerical_cols,
            'is_fitted': self.is_fitted
        }
        
        joblib.dump(preprocessor_data, filepath)
    
    @classmethod
    def load(cls, filepath):
        """
        Load a fitted preprocessor from a file.
        
        Args:
            filepath (str): Path to the saved preprocessor
            
        Returns:
            DataPreprocessor: Loaded preprocessor instance
        """
        preprocessor_data = joblib.load(filepath)
        
        preprocessor = cls()
        preprocessor.num_imputer = preprocessor_data['num_imputer']
        preprocessor.cat_imputer = preprocessor_data['cat_imputer']
        preprocessor.target_encoder = preprocessor_data['target_encoder']
        preprocessor.scaler = preprocessor_data['scaler']
        preprocessor.categorical_cols = preprocessor_data['categorical_cols']
        preprocessor.numerical_cols = preprocessor_data['numerical_cols']
        preprocessor.is_fitted = preprocessor_data['is_fitted']
        
        return preprocessor


class ModelPreprocessor:
    """
    Preprocessor that uses the exact same preprocessing components
    as saved in the trained model package.
    """

    def __init__(self, model_package_path):
        """
        Initialize with the saved model package.

        Args:
            model_package_path (str): Path to the stacking_ensemble.joblib file
        """
        self.model_package = joblib.load(model_package_path)
        self.num_imputer = self.model_package.get('num_imputer')
        self.cat_imputer = self.model_package.get('cat_imputer')
        self.target_encoder = self.model_package.get('target_encoder')
        self.variance_selector = self.model_package.get('variance_selector')
        self.scaler = self.model_package.get('scaler')
        self.feature_selector = self.model_package.get('feature_selector')
        self.selected_features = self.model_package.get('selected_features', [])
        self.feature_names_after_var = self.model_package.get('feature_names_after_var', [])
        self.categorical_cols = self.model_package.get('categorical_cols', [])
        self.numerical_cols = self.model_package.get('numerical_cols', [])

        # Create a mapping for the 30 selected features to the post-variance-selection features
        self._create_feature_mapping()

    def _create_feature_mapping(self):
        """Create a mapping from selected features to post-variance features."""
        # Create a DataFrame with the 56 features after variance selection
        # Set default values for missing features
        self.feature_defaults = {}

        for feature in self.feature_names_after_var:
            if feature in self.selected_features:
                # This feature is in our extracted set
                self.feature_defaults[feature] = None  # Will be filled from input
            elif feature in self.categorical_cols:
                # Default value for categorical features
                self.feature_defaults[feature] = 'unknown'
            else:
                # Default value for numerical features
                self.feature_defaults[feature] = 0

    def preprocess_for_prediction(self, X):
        """
        Preprocess data for prediction using a simplified approach.

        Since the preprocessing pipeline is complex and the imputers/encoders
        were fitted on the original dataset, we'll use a simplified approach
        that directly processes our 30 features.

        Args:
            X (pd.DataFrame): Feature data with exactly 30 selected features

        Returns:
            np.ndarray: Preprocessed data ready for model prediction
        """
        try:
            # Create the full feature set expected by the scaler
            X_full = self._create_full_feature_set_simple(X)

            # Apply target encoding to TLD if needed
            if 'TLD' in X_full.columns and self.target_encoder:
                # Create a temporary DataFrame for target encoding
                X_encoded = X_full.copy()
                X_encoded = self.target_encoder.transform(X_encoded)
            else:
                X_encoded = X_full

            # Apply scaling
            X_scaled = self.scaler.transform(X_encoded)

            # Apply feature selection to get final 30 features
            X_final = self.feature_selector.transform(X_scaled)

            return X_final

        except Exception as e:
            # Fallback: try to use the features directly if they match
            if list(X.columns) == self.selected_features:
                # Handle TLD encoding manually
                X_processed = X.copy()

                # Simple TLD encoding: map to numeric values
                if 'TLD' in X_processed.columns:
                    # Use a simple mapping for common TLDs
                    tld_mapping = {
                        'com': 1, 'org': 2, 'net': 3, 'edu': 4, 'gov': 5,
                        'mil': 6, 'int': 7, 'unknown': 0
                    }
                    X_processed['TLD'] = X_processed['TLD'].map(tld_mapping).fillna(0)

                # Convert to numpy array
                return X_processed.values
            else:
                raise e

    def _create_full_feature_set_simple(self, X_selected):
        """
        Create a full feature set by adding missing features with default values.

        Args:
            X_selected (pd.DataFrame): DataFrame with 30 selected features

        Returns:
            pd.DataFrame: DataFrame with all 56 features needed for the scaler
        """
        # Create a new DataFrame with all required features
        X_full = pd.DataFrame(index=X_selected.index)

        # Add all features in the correct order
        for feature in self.feature_names_after_var:
            if feature in X_selected.columns:
                # Use the actual value from our extracted features
                X_full[feature] = X_selected[feature]
            else:
                # Use default value
                X_full[feature] = self.feature_defaults[feature]

        return X_full

    def preprocess_full_pipeline(self, X):
        """
        Apply the full preprocessing pipeline as done during training.

        Args:
            X (pd.DataFrame): Feature data with features after variance selection

        Returns:
            np.ndarray: Preprocessed data ready for model prediction
        """
        X_processed = X.copy()

        # Step 1: Handle Missing Values
        # Apply imputation only to columns that exist and need it
        if self.num_imputer:
            # Get numerical columns that exist in our data
            existing_num_cols = [col for col in self.numerical_cols if col in X_processed.columns]
            if existing_num_cols:
                X_processed[existing_num_cols] = self.num_imputer.transform(X_processed[existing_num_cols])

        if self.cat_imputer:
            # Get categorical columns that exist in our data
            existing_cat_cols = [col for col in self.categorical_cols if col in X_processed.columns]
            if existing_cat_cols:
                X_processed[existing_cat_cols] = self.cat_imputer.transform(X_processed[existing_cat_cols])

        # Step 2: Target Encoding for Categorical Features
        if self.target_encoder:
            # Apply target encoding to categorical columns
            existing_cat_cols = [col for col in self.categorical_cols if col in X_processed.columns]
            if existing_cat_cols:
                X_processed = self.target_encoder.transform(X_processed)

        # Step 3: Standard Scaling
        # The scaler expects features in the order of feature_names_after_var
        if self.scaler:
            X_scaled = self.scaler.transform(X_processed)
        else:
            X_scaled = X_processed.values

        # Step 4: Feature Selection
        # Select only the final 30 features
        if self.feature_selector:
            X_final = self.feature_selector.transform(X_scaled)
            return X_final

        return X_scaled
