#!/usr/bin/env python3

import joblib
import pandas as pd

def examine_model():
    """Examine the saved model package to understand the features and preprocessing."""
    
    try:
        # Load the model package
        model_package = joblib.load('stacking_ensemble.joblib')
        
        print('=' * 60)
        print('MODEL PACKAGE EXAMINATION')
        print('=' * 60)
        
        print('\nModel package keys:')
        for key in model_package.keys():
            print(f'  {key}: {type(model_package[key])}')
        
        print('\n' + '=' * 40)
        print('SELECTED FEATURES (30 features)')
        print('=' * 40)
        selected_features = model_package.get('selected_features', [])
        for i, feature in enumerate(selected_features):
            print(f'  {i+1:2d}. {feature}')
        
        print('\n' + '=' * 40)
        print('CATEGORICAL COLUMNS')
        print('=' * 40)
        categorical_cols = model_package.get('categorical_cols', [])
        print(f'Categorical columns: {categorical_cols}')
        
        print('\n' + '=' * 40)
        print('NUMERICAL COLUMNS')
        print('=' * 40)
        numerical_cols = model_package.get('numerical_cols', [])
        print(f'Number of numerical columns: {len(numerical_cols)}')
        for i, col in enumerate(numerical_cols[:10]):  # Show first 10
            print(f'  {i+1:2d}. {col}')
        if len(numerical_cols) > 10:
            print(f'  ... and {len(numerical_cols) - 10} more')
        
        print('\n' + '=' * 40)
        print('FEATURES AFTER VARIANCE SELECTION')
        print('=' * 40)
        feature_names_after_var = model_package.get('feature_names_after_var', [])
        print(f'Number of features after variance selection: {len(feature_names_after_var)}')
        for i, feature in enumerate(feature_names_after_var[:15]):  # Show first 15
            print(f'  {i+1:2d}. {feature}')
        if len(feature_names_after_var) > 15:
            print(f'  ... and {len(feature_names_after_var) - 15} more')
        
        # Save the feature lists to files for reference
        if selected_features:
            pd.DataFrame({'feature_name': selected_features}).to_csv('selected_features_list.csv', index=False)
            print('\n✅ Selected features saved to selected_features_list.csv')
        
        if feature_names_after_var:
            pd.DataFrame({'feature_name': feature_names_after_var}).to_csv('features_after_variance.csv', index=False)
            print('✅ Features after variance selection saved to features_after_variance.csv')
        
        return selected_features, categorical_cols, numerical_cols, feature_names_after_var
        
    except Exception as e:
        print(f'Error examining model: {e}')
        return None, None, None, None

if __name__ == '__main__':
    examine_model()
