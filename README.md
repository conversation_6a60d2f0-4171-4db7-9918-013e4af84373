# Phishing URL Detection API

A production-ready FastAPI web application that predicts whether a URL is legitimate (0) or phishing (1) based on extracted features using a trained machine learning model.

## Features

- **Comprehensive Feature Extraction**: Extracts exactly 30 features from URLs by fetching actual web content
- **Machine Learning Prediction**: Uses a trained stacking ensemble model for accurate phishing detection
- **REST API**: Clean API endpoints for integration with other systems
- **Web Interface**: User-friendly web interface for manual URL checking
- **Production Ready**: Proper error handling, logging, and validation

## Architecture

### Core Components

1. **Feature Extractor** (`feature_extractor.py`)
   - Extracts 30 specific features from URLs
   - Fetches actual web content via HTTP requests
   - Handles network errors and timeouts gracefully
   - Calculates advanced metrics like Shannon entropy and Kolmogorov complexity

2. **Preprocessor** (`preprocessor.py`)
   - Applies exact preprocessing pipeline from training
   - Missing value imputation (median for numeric, most frequent for categorical)
   - Target encoding for categorical features (TLD)
   - Standard scaling for all features

3. **FastAPI Application** (`app.py`)
   - REST API endpoints for URL prediction
   - Web interface for manual testing
   - Comprehensive error handling and logging
   - Health check endpoint

## Features Extracted

The system extracts exactly 30 features in this precise order:

1. **LengthOfURL** - Total length of the URL
2. **URLComplexity** - Complexity score based on various factors
3. **TLD** - Top-level domain (encoded)
4. **LetterCntInURL** - Count of letters in URL
5. **URLLetterRatio** - Ratio of letters to total characters
6. **DigitCntInURL** - Count of digits in URL
7. **URLDigitRatio** - Ratio of digits to total characters
8. **OtherSpclCharCntInURL** - Count of special characters
9. **HavingPath** - Whether URL has a path component
10. **PathLength** - Length of the path component
11. **HasSSL** - Whether URL uses HTTPS
12. **LineOfCode** - Number of lines in HTML content
13. **LongestLineLength** - Length of longest line in HTML
14. **HasFavicon** - Whether page has a favicon
15. **HasRobotsBlocked** - Whether robots.txt blocks access
16. **IsSelfRedirects** - Number of redirects
17. **HasDescription** - Whether page has meta description
18. **HasSubmitButton** - Whether page has submit buttons/forms
19. **HasCopyrightInfoKey** - Whether page has copyright information
20. **CntImages** - Count of images on page
21. **CntFilesCSS** - Count of CSS file references
22. **CntFilesJS** - Count of JavaScript file references
23. **CntSelfHRef** - Count of internal links
24. **CntEmptyRef** - Count of empty references
25. **CntExternalRef** - Count of external references
26. **CntIFrame** - Count of iframes
27. **UniqueFeatureCnt** - Count of unique HTML features
28. **ShannonEntropy** - Shannon entropy of URL
29. **KolmogorovComplexity** - Estimated Kolmogorov complexity
30. **LikelinessIndex** - Composite likeliness score

## API Endpoints

### POST /predict
Predict whether a single URL is phishing or legitimate.

**Request:**
```json
{
    "url": "https://example.com"
}
```

**Response:**
```json
{
    "url": "https://example.com",
    "prediction": 0,
    "prediction_label": "Legitimate",
    "confidence": 0.95,
    "probabilities": {
        "legitimate": 0.95,
        "phishing": 0.05
    },
    "processing_time": 1.23,
    "features": {...},
    "status": "success"
}
```

### POST /predict/batch
Predict multiple URLs in batch (max 100 URLs).

**Request:**
```json
["https://example1.com", "https://example2.com"]
```

### GET /health
Health check endpoint.

### GET /
Web interface for manual URL testing.

## Installation and Usage

### Prerequisites
- Python 3.8+
- Required packages (install via pip):
  - fastapi
  - uvicorn
  - pandas
  - numpy
  - scikit-learn
  - requests
  - beautifulsoup4
  - category-encoders
  - joblib

### Running the Application

1. **Start the server:**
   ```bash
   python app.py
   ```
   
   Or using uvicorn directly:
   ```bash
   uvicorn app:app --host 0.0.0.0 --port 8000 --reload
   ```

2. **Access the application:**
   - Web Interface: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Testing the API

**Using curl:**
```bash
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://example.com"}'
```

**Using Python requests:**
```python
import requests

response = requests.post(
    "http://localhost:8000/predict",
    json={"url": "https://example.com"}
)
print(response.json())
```

## Model Information

The application uses a pre-trained stacking ensemble model (`stacking_ensemble.joblib`) that includes:
- Base models: LightGBM, XGBoost, Random Forest, Extra Trees
- Meta learner: Logistic Regression with RBF kernel approximation
- Preprocessing components: Imputers, encoders, scalers, feature selectors

## Error Handling

The application includes comprehensive error handling for:
- Invalid URLs
- Network timeouts and failures
- Model prediction errors
- Missing or corrupted model files
- Invalid input data

## Performance Considerations

- **Timeout**: HTTP requests timeout after 10 seconds
- **Retries**: Up to 2 retry attempts for failed requests
- **Batch Limit**: Maximum 100 URLs per batch request
- **Caching**: Session reuse for HTTP requests
- **Async**: FastAPI provides async request handling

## Security Notes

- The application fetches content from external URLs
- Proper timeout and retry limits prevent hanging requests
- Input validation prevents malicious input
- No sensitive data is logged or stored

## Troubleshooting

### Common Issues

1. **Model file not found**: Ensure `stacking_ensemble.joblib` is in the same directory
2. **Network timeouts**: Some URLs may be slow to respond; this is handled gracefully
3. **Feature extraction failures**: The system provides default values when content cannot be fetched
4. **Memory usage**: Large batch requests may consume significant memory

### Logs

The application provides detailed logging for debugging:
- Feature extraction progress
- Preprocessing steps
- Prediction results
- Error details

## Development

### Testing Feature Extraction
```python
from feature_extractor import URLFeatureExtractor

extractor = URLFeatureExtractor()
features = extractor.extract_features("https://example.com")
print(features.shape)  # Should be (1, 30)
```

### Testing Preprocessing
```python
from preprocessor import PhishingPreprocessor

preprocessor = PhishingPreprocessor()
processed = preprocessor.preprocess(features)
predictions, probabilities = preprocessor.predict(features)
```

## License

This project is for educational and research purposes.
