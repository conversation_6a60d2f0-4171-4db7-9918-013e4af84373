#!/usr/bin/env python3
"""
URL Feature Extraction Module for Phishing Detection

This module extracts exactly 30 features from URLs in the precise order and naming
required by the trained model. It fetches actual web content via HTTP requests
to extract comprehensive features for phishing detection.

Features extracted (in order):
1. LengthOfURL
2. URLComplexity  
3. TLD
4. LetterCntInURL
5. URLLetterRatio
6. DigitCntInURL
7. URLDigitRatio
8. OtherSpclCharCntInURL
9. HavingPath
10. PathLength
11. HasSSL
12. LineOfCode
13. LongestLineLength
14. HasFavicon
15. HasRobotsBlocked
16. IsSelfRedirects
17. HasDescription
18. HasSubmitButton
19. HasCopyrightInfoKey
20. CntImages
21. CntFilesCSS
22. CntFilesJS
23. CntSelfHRef
24. CntEmptyRef
25. CntExternalRef
26. CntIFrame
27. UniqueFeatureCnt
28. ShannonEntropy
29. KolmogorovComplexity
30. LikelinessIndex
"""

import pandas as pd
import numpy as np
import requests
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
import re
import math
import time
import warnings
from collections import Counter
import socket
import ssl
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

warnings.filterwarnings('ignore')


class URLFeatureExtractor:
    """
    Comprehensive URL feature extractor for phishing detection.
    
    Extracts 30 specific features from URLs by analyzing both the URL structure
    and the actual web content fetched via HTTP requests.
    """
    
    def __init__(self, timeout=10, max_retries=2):
        """
        Initialize the feature extractor.
        
        Args:
            timeout (int): Request timeout in seconds
            max_retries (int): Maximum number of retry attempts
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = self._create_session()
        
        # Common TLDs for encoding
        self.common_tlds = {
            'com': 1, 'org': 2, 'net': 3, 'edu': 4, 'gov': 5,
            'mil': 6, 'int': 7, 'co': 8, 'uk': 9, 'de': 10,
            'fr': 11, 'it': 12, 'es': 13, 'ru': 14, 'cn': 15,
            'jp': 16, 'br': 17, 'au': 18, 'ca': 19, 'in': 20
        }
    
    def _create_session(self):
        """Create a requests session with retry strategy and proper headers."""
        session = requests.Session()
        
        # Retry strategy
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set realistic headers
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        return session
    
    def extract_features(self, url):
        """
        Extract all 30 features from a given URL.
        
        Args:
            url (str): The URL to analyze
            
        Returns:
            pd.DataFrame: DataFrame with exactly 30 features in the correct order
        """
        try:
            # Normalize URL
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url
            
            # Parse URL
            parsed_url = urlparse(url)
            
            # Fetch web content
            html_content, response_info = self._fetch_content(url)
            
            # Extract all features
            features = {}
            
            # 1. LengthOfURL
            features['LengthOfURL'] = len(url)
            
            # 2. URLComplexity
            features['URLComplexity'] = self._calculate_url_complexity(url)
            
            # 3. TLD
            features['TLD'] = self._extract_tld(parsed_url.netloc)
            
            # 4. LetterCntInURL
            features['LetterCntInURL'] = sum(1 for c in url if c.isalpha())
            
            # 5. URLLetterRatio
            features['URLLetterRatio'] = features['LetterCntInURL'] / len(url) if len(url) > 0 else 0
            
            # 6. DigitCntInURL
            features['DigitCntInURL'] = sum(1 for c in url if c.isdigit())
            
            # 7. URLDigitRatio
            features['URLDigitRatio'] = features['DigitCntInURL'] / len(url) if len(url) > 0 else 0
            
            # 8. OtherSpclCharCntInURL
            features['OtherSpclCharCntInURL'] = self._count_special_chars(url)
            
            # 9. HavingPath
            features['HavingPath'] = 1 if parsed_url.path and parsed_url.path != '/' else 0
            
            # 10. PathLength
            features['PathLength'] = len(parsed_url.path) if parsed_url.path else 0
            
            # 11. HasSSL
            features['HasSSL'] = 1 if url.startswith('https://') else 0
            
            # Features 12-30 require HTML content analysis
            if html_content:
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 12. LineOfCode
                features['LineOfCode'] = len(html_content.split('\n'))
                
                # 13. LongestLineLength
                lines = html_content.split('\n')
                features['LongestLineLength'] = max(len(line) for line in lines) if lines else 0
                
                # 14. HasFavicon
                features['HasFavicon'] = self._has_favicon(soup, url)
                
                # 15. HasRobotsBlocked
                features['HasRobotsBlocked'] = self._check_robots_blocked(url)
                
                # 16. IsSelfRedirects
                features['IsSelfRedirects'] = response_info.get('redirects', 0)
                
                # 17. HasDescription
                features['HasDescription'] = self._has_description(soup)
                
                # 18. HasSubmitButton
                features['HasSubmitButton'] = self._has_submit_button(soup)
                
                # 19. HasCopyrightInfoKey (note: includes trailing space in name)
                features['HasCopyrightInfoKey '] = self._has_copyright_info(soup)
                
                # 20. CntImages
                features['CntImages'] = len(soup.find_all('img'))
                
                # 21. CntFilesCSS
                features['CntFilesCSS'] = self._count_css_files(soup)
                
                # 22. CntFilesJS
                features['CntFilesJS'] = self._count_js_files(soup)
                
                # 23. CntSelfHRef
                features['CntSelfHRef'] = self._count_self_href(soup, parsed_url.netloc)
                
                # 24. CntEmptyRef
                features['CntEmptyRef'] = self._count_empty_ref(soup)
                
                # 25. CntExternalRef
                features['CntExternalRef'] = self._count_external_ref(soup, parsed_url.netloc)
                
                # 26. CntIFrame
                features['CntIFrame'] = len(soup.find_all('iframe'))
                
                # 27. UniqueFeatureCnt
                features['UniqueFeatureCnt'] = self._count_unique_features(soup)
                
                # 28. ShannonEntropy
                features['ShannonEntropy'] = self._calculate_shannon_entropy(url)
                
                # 29. KolmogorovComplexity
                features['KolmogorovComplexity'] = self._calculate_kolmogorov_complexity(url)
                
                # 30. LikelinessIndex
                features['LikelinessIndex'] = self._calculate_likeliness_index(features, soup)
                
            else:
                # Default values when content cannot be fetched
                default_features = {
                    'LineOfCode': 0, 'LongestLineLength': 0, 'HasFavicon': 0,
                    'HasRobotsBlocked': 0, 'IsSelfRedirects': 0, 'HasDescription': 0,
                    'HasSubmitButton': 0, 'HasCopyrightInfoKey ': 0, 'CntImages': 0,
                    'CntFilesCSS': 0, 'CntFilesJS': 0, 'CntSelfHRef': 0,
                    'CntEmptyRef': 0, 'CntExternalRef': 0, 'CntIFrame': 0,
                    'UniqueFeatureCnt': 0, 'ShannonEntropy': self._calculate_shannon_entropy(url),
                    'KolmogorovComplexity': self._calculate_kolmogorov_complexity(url),
                    'LikelinessIndex': 0.5
                }
                features.update(default_features)
            
            # Create DataFrame with exact feature order
            feature_order = [
                'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
                'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
                'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
                'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
                'HasCopyrightInfoKey ', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
                'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
                'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
            ]
            
            # Ensure all features are present and in correct order
            ordered_features = {feature: features.get(feature, 0) for feature in feature_order}
            
            return pd.DataFrame([ordered_features])
            
        except Exception as e:
            print(f"Error extracting features from {url}: {e}")
            # Return default feature values
            return self._get_default_features()

    def _fetch_content(self, url):
        """
        Fetch web content from URL with error handling.

        Args:
            url (str): URL to fetch

        Returns:
            tuple: (html_content, response_info)
        """
        try:
            response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
            response.raise_for_status()

            response_info = {
                'status_code': response.status_code,
                'redirects': len(response.history),
                'final_url': response.url
            }

            return response.text, response_info

        except Exception as e:
            print(f"Failed to fetch content from {url}: {e}")
            return None, {'status_code': 0, 'redirects': 0, 'final_url': url}

    def _calculate_url_complexity(self, url):
        """Calculate URL complexity based on various factors."""
        complexity = 0

        # Length factor
        complexity += len(url) / 100

        # Special characters
        special_chars = set('!@#$%^&*()_+-=[]{}|;:,.<>?')
        complexity += sum(1 for c in url if c in special_chars)

        # Subdomain count
        parsed = urlparse(url)
        subdomain_count = len(parsed.netloc.split('.')) - 2
        complexity += subdomain_count * 2

        # Path depth
        path_depth = len([p for p in parsed.path.split('/') if p])
        complexity += path_depth

        # Query parameters
        if parsed.query:
            complexity += len(parsed.query.split('&'))

        return complexity

    def _extract_tld(self, netloc):
        """Extract and encode TLD from netloc."""
        if not netloc:
            return 'unknown'

        parts = netloc.split('.')
        if len(parts) >= 2:
            tld = parts[-1].lower()
            return tld
        return 'unknown'

    def _count_special_chars(self, url):
        """Count special characters in URL (excluding letters, digits, and common URL chars)."""
        common_url_chars = set('://.?&=_-')
        special_chars = set('!@#$%^*()+=[]{}|;,<>')
        return sum(1 for c in url if c in special_chars)

    def _has_favicon(self, soup, base_url):
        """Check if the page has a favicon."""
        # Check for favicon link tags
        favicon_links = soup.find_all('link', rel=lambda x: x and 'icon' in x.lower())
        if favicon_links:
            return 1

        # Check for default favicon.ico
        try:
            favicon_url = urljoin(base_url, '/favicon.ico')
            response = self.session.head(favicon_url, timeout=5)
            return 1 if response.status_code == 200 else 0
        except:
            return 0

    def _check_robots_blocked(self, url):
        """Check if robots.txt blocks access."""
        try:
            parsed = urlparse(url)
            robots_url = f"{parsed.scheme}://{parsed.netloc}/robots.txt"
            response = self.session.get(robots_url, timeout=5)

            if response.status_code == 200:
                robots_content = response.text.lower()
                if 'disallow: /' in robots_content:
                    return 1
            return 0
        except:
            return 0

    def _has_description(self, soup):
        """Check if page has meta description."""
        description = soup.find('meta', attrs={'name': 'description'})
        return 1 if description and description.get('content') else 0

    def _has_submit_button(self, soup):
        """Check if page has submit buttons or forms."""
        submit_buttons = soup.find_all(['input'], type='submit')
        forms = soup.find_all('form')
        buttons = soup.find_all('button')
        return 1 if (submit_buttons or forms or buttons) else 0

    def _has_copyright_info(self, soup):
        """Check if page has copyright information."""
        text_content = soup.get_text().lower()
        copyright_indicators = ['copyright', '©', '&copy;', 'all rights reserved']
        return 1 if any(indicator in text_content for indicator in copyright_indicators) else 0

    def _count_css_files(self, soup):
        """Count CSS file references."""
        css_links = soup.find_all('link', rel='stylesheet')
        style_tags = soup.find_all('style')
        return len(css_links) + len(style_tags)

    def _count_js_files(self, soup):
        """Count JavaScript file references."""
        js_scripts = soup.find_all('script', src=True)
        inline_scripts = soup.find_all('script', src=False)
        return len(js_scripts) + len(inline_scripts)

    def _count_self_href(self, soup, domain):
        """Count links pointing to the same domain."""
        links = soup.find_all('a', href=True)
        self_links = 0

        for link in links:
            href = link['href']
            if href.startswith('/') or domain in href:
                self_links += 1

        return self_links

    def _count_empty_ref(self, soup):
        """Count empty or placeholder references."""
        empty_refs = 0

        # Empty href attributes
        empty_links = soup.find_all('a', href=['', '#', 'javascript:void(0)'])
        empty_refs += len(empty_links)

        # Empty src attributes
        empty_imgs = soup.find_all('img', src=['', '#'])
        empty_refs += len(empty_imgs)

        return empty_refs

    def _count_external_ref(self, soup, domain):
        """Count references to external domains."""
        external_refs = 0

        # External links
        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href']
            if href.startswith('http') and domain not in href:
                external_refs += 1

        # External images
        images = soup.find_all('img', src=True)
        for img in images:
            src = img['src']
            if src.startswith('http') and domain not in src:
                external_refs += 1

        # External scripts
        scripts = soup.find_all('script', src=True)
        for script in scripts:
            src = script['src']
            if src.startswith('http') and domain not in src:
                external_refs += 1

        return external_refs

    def _count_unique_features(self, soup):
        """Count unique HTML features/elements."""
        unique_features = set()

        # Count unique tag types
        for tag in soup.find_all():
            unique_features.add(tag.name)

        # Count unique attributes
        for tag in soup.find_all():
            for attr in tag.attrs:
                unique_features.add(f"attr_{attr}")

        return len(unique_features)

    def _calculate_shannon_entropy(self, text):
        """Calculate Shannon entropy of the text."""
        if not text:
            return 0

        # Count character frequencies
        char_counts = Counter(text)
        text_length = len(text)

        # Calculate entropy
        entropy = 0
        for count in char_counts.values():
            probability = count / text_length
            if probability > 0:
                entropy -= probability * math.log2(probability)

        return entropy

    def _calculate_kolmogorov_complexity(self, text):
        """
        Estimate Kolmogorov complexity using compression ratio.
        This is an approximation since true Kolmogorov complexity is uncomputable.
        """
        if not text:
            return 0

        try:
            import zlib
            compressed = zlib.compress(text.encode('utf-8'))
            complexity = len(compressed) / len(text) if len(text) > 0 else 0
            return complexity
        except:
            # Fallback: use character diversity as complexity measure
            unique_chars = len(set(text))
            total_chars = len(text)
            return unique_chars / total_chars if total_chars > 0 else 0

    def _calculate_likeliness_index(self, features, soup):
        """
        Calculate a likeliness index based on various features.
        This is a composite score indicating how "normal" the website appears.
        """
        score = 0.5  # Start with neutral score

        # URL-based factors
        if features.get('HasSSL', 0):
            score += 0.1

        if features.get('LengthOfURL', 0) < 100:
            score += 0.05
        else:
            score -= 0.05

        # Content-based factors
        if features.get('HasFavicon', 0):
            score += 0.05

        if features.get('HasDescription', 0):
            score += 0.05

        if features.get('HasCopyrightInfoKey', 0):
            score += 0.1

        # Structural factors
        if features.get('CntImages', 0) > 0:
            score += 0.05

        if features.get('CntFilesCSS', 0) > 0:
            score += 0.05

        # Penalize suspicious patterns
        if features.get('CntExternalRef', 0) > features.get('CntSelfHRef', 0):
            score -= 0.1

        if features.get('URLComplexity', 0) > 20:
            score -= 0.1

        # Ensure score is between 0 and 1
        return max(0, min(1, score))

    def _get_default_features(self):
        """Return DataFrame with default feature values when extraction fails."""
        feature_order = [
            'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
            'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
            'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
            'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
            'HasCopyrightInfoKey ', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
            'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
            'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
        ]

        # Default values for failed extraction
        default_values = {
            'LengthOfURL': 0, 'URLComplexity': 0, 'TLD': 'unknown', 'LetterCntInURL': 0,
            'URLLetterRatio': 0, 'DigitCntInURL': 0, 'URLDigitRatio': 0,
            'OtherSpclCharCntInURL': 0, 'HavingPath': 0, 'PathLength': 0, 'HasSSL': 0,
            'LineOfCode': 0, 'LongestLineLength': 0, 'HasFavicon': 0, 'HasRobotsBlocked': 0,
            'IsSelfRedirects': 0, 'HasDescription': 0, 'HasSubmitButton': 0,
            'HasCopyrightInfoKey ': 0, 'CntImages': 0, 'CntFilesCSS': 0, 'CntFilesJS': 0,
            'CntSelfHRef': 0, 'CntEmptyRef': 0, 'CntExternalRef': 0, 'CntIFrame': 0,
            'UniqueFeatureCnt': 0, 'ShannonEntropy': 0, 'KolmogorovComplexity': 0,
            'LikelinessIndex': 0.5
        }

        return pd.DataFrame([default_values])


def extract_url_features(url):
    """
    Convenience function to extract features from a single URL.

    Args:
        url (str): URL to analyze

    Returns:
        pd.DataFrame: DataFrame with 30 features
    """
    extractor = URLFeatureExtractor()
    return extractor.extract_features(url)


if __name__ == "__main__":
    # Test the feature extractor
    test_urls = [
        "https://www.google.com",
        "http://example.com",
        "https://github.com"
    ]

    extractor = URLFeatureExtractor()

    for url in test_urls:
        print(f"\nExtracting features from: {url}")
        features = extractor.extract_features(url)
        print(f"Features shape: {features.shape}")
        print(f"Feature columns: {list(features.columns)}")
        print(f"Sample values: {features.iloc[0].to_dict()}")
