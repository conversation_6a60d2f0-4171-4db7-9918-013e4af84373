#!/usr/bin/env python3
"""
FastAPI Application for Phishing URL Detection

This application provides a REST API and web interface for detecting phishing URLs
using a trained machine learning model. It extracts 30 features from URLs and
applies the exact preprocessing pipeline used during training.

Endpoints:
- GET /: Web interface
- POST /predict: URL prediction API
- GET /health: Health check
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel, validator
import uvicorn
import logging
import time
from typing import Optional, Dict, Any
import traceback
import os

# Import our custom modules
from feature_extractor import URLFeatureExtractor
from preprocessor import PhishingPreprocessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Phishing URL Detection API",
    description="Production-ready API for detecting phishing URLs using machine learning",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Global variables for model components
feature_extractor = None
preprocessor = None
model = None

# Request/Response models
class URLRequest(BaseModel):
    url: str
    
    @validator('url')
    def validate_url(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('URL cannot be empty')
        
        # Basic URL validation
        url = v.strip()
        if not any(url.startswith(prefix) for prefix in ['http://', 'https://', 'www.', 'ftp://']):
            # Add http:// if no protocol specified
            if not url.startswith(('www.', 'ftp://')):
                url = 'http://' + url
            elif url.startswith('www.'):
                url = 'http://' + url
        
        return url

class PredictionResponse(BaseModel):
    url: str
    prediction: int
    prediction_label: str
    confidence: Optional[float] = None
    probabilities: Optional[Dict[str, float]] = None
    processing_time: float
    features: Optional[Dict[str, Any]] = None
    status: str = "success"

class ErrorResponse(BaseModel):
    error: str
    detail: str
    status: str = "error"

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize model components on startup."""
    global feature_extractor, preprocessor, model
    
    try:
        logger.info("🚀 Starting Phishing Detection API...")
        
        # Initialize feature extractor
        logger.info("📊 Loading feature extractor...")
        feature_extractor = URLFeatureExtractor(timeout=10, max_retries=2)
        
        # Initialize preprocessor and model
        logger.info("🤖 Loading model and preprocessor...")
        preprocessor = PhishingPreprocessor('stacking_ensemble.joblib')
        model = preprocessor.get_model()
        
        logger.info("✅ All components loaded successfully!")
        logger.info(f"📋 Model expects {len(preprocessor.selected_features)} features")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize components: {e}")
        logger.error(traceback.format_exc())
        raise

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "components": {
            "feature_extractor": feature_extractor is not None,
            "preprocessor": preprocessor is not None,
            "model": model is not None
        }
    }

# Main prediction endpoint
@app.post("/predict", response_model=PredictionResponse)
async def predict_url(request: URLRequest):
    """
    Predict whether a URL is legitimate (0) or phishing (1).
    
    Args:
        request: URLRequest containing the URL to analyze
        
    Returns:
        PredictionResponse with prediction results
    """
    start_time = time.time()
    
    try:
        url = request.url
        logger.info(f"🔍 Analyzing URL: {url}")
        
        # Extract features
        logger.info("📊 Extracting features...")
        features_df = feature_extractor.extract_features(url)
        
        if features_df is None or features_df.empty:
            raise HTTPException(
                status_code=400,
                detail="Failed to extract features from URL"
            )
        
        logger.info(f"✅ Extracted {features_df.shape[1]} features")
        
        # Make prediction
        logger.info("🤖 Making prediction...")
        predictions, probabilities = preprocessor.predict(features_df)
        
        # Process results
        prediction = int(predictions[0])
        prediction_label = "Phishing" if prediction == 1 else "Legitimate"
        
        # Calculate confidence
        confidence = None
        prob_dict = None
        if probabilities is not None:
            prob_dict = {
                "legitimate": float(probabilities[0][0]),
                "phishing": float(probabilities[0][1])
            }
            confidence = float(max(probabilities[0]))
        
        processing_time = time.time() - start_time
        
        logger.info(f"✅ Prediction: {prediction_label} (confidence: {confidence:.3f if confidence else 'N/A'})")
        
        return PredictionResponse(
            url=url,
            prediction=prediction,
            prediction_label=prediction_label,
            confidence=confidence,
            probabilities=prob_dict,
            processing_time=processing_time,
            features=features_df.iloc[0].to_dict() if features_df is not None else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error processing URL {request.url}: {e}")
        logger.error(traceback.format_exc())
        
        processing_time = time.time() - start_time
        
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

# Batch prediction endpoint
@app.post("/predict/batch")
async def predict_batch(urls: list[str]):
    """
    Predict multiple URLs in batch.
    
    Args:
        urls: List of URLs to analyze
        
    Returns:
        List of prediction results
    """
    if len(urls) > 100:
        raise HTTPException(
            status_code=400,
            detail="Maximum 100 URLs allowed per batch request"
        )
    
    results = []
    
    for url in urls:
        try:
            request = URLRequest(url=url)
            result = await predict_url(request)
            results.append(result.dict())
        except Exception as e:
            results.append({
                "url": url,
                "error": str(e),
                "status": "error"
            })
    
    return {"results": results, "total": len(urls), "processed": len(results)}

# Web interface endpoint
@app.get("/", response_class=HTMLResponse)
async def web_interface():
    """Serve the web interface."""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Phishing URL Detection</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            
            .container {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                padding: 40px;
                max-width: 600px;
                width: 100%;
            }
            
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            
            .header h1 {
                color: #333;
                font-size: 2.5em;
                margin-bottom: 10px;
            }
            
            .header p {
                color: #666;
                font-size: 1.1em;
            }
            
            .form-group {
                margin-bottom: 20px;
            }
            
            .form-group label {
                display: block;
                margin-bottom: 8px;
                color: #333;
                font-weight: 600;
            }
            
            .form-group input {
                width: 100%;
                padding: 15px;
                border: 2px solid #e1e5e9;
                border-radius: 10px;
                font-size: 16px;
                transition: border-color 0.3s;
            }
            
            .form-group input:focus {
                outline: none;
                border-color: #667eea;
            }
            
            .btn {
                width: 100%;
                padding: 15px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.2s;
            }
            
            .btn:hover {
                transform: translateY(-2px);
            }
            
            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
            
            .result {
                margin-top: 30px;
                padding: 20px;
                border-radius: 10px;
                display: none;
            }
            
            .result.legitimate {
                background: #d4edda;
                border: 1px solid #c3e6cb;
                color: #155724;
            }
            
            .result.phishing {
                background: #f8d7da;
                border: 1px solid #f5c6cb;
                color: #721c24;
            }
            
            .result.error {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
            }
            
            .loading {
                text-align: center;
                margin-top: 20px;
                display: none;
            }
            
            .spinner {
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 0 auto 10px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .details {
                margin-top: 15px;
                font-size: 0.9em;
                opacity: 0.8;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🛡️ Phishing Detection</h1>
                <p>Enter a URL to check if it's legitimate or phishing</p>
            </div>
            
            <form id="urlForm">
                <div class="form-group">
                    <label for="url">URL to analyze:</label>
                    <input type="text" id="url" name="url" placeholder="https://example.com" required>
                </div>
                <button type="submit" class="btn" id="submitBtn">Analyze URL</button>
            </form>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Analyzing URL...</p>
            </div>
            
            <div class="result" id="result">
                <div id="resultContent"></div>
            </div>
        </div>
        
        <script>
            document.getElementById('urlForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const url = document.getElementById('url').value;
                const submitBtn = document.getElementById('submitBtn');
                const loading = document.getElementById('loading');
                const result = document.getElementById('result');
                const resultContent = document.getElementById('resultContent');
                
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.textContent = 'Analyzing...';
                loading.style.display = 'block';
                result.style.display = 'none';
                
                try {
                    const response = await fetch('/predict', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ url: url })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        // Success
                        const isPhishing = data.prediction === 1;
                        const confidence = data.confidence ? (data.confidence * 100).toFixed(1) : 'N/A';
                        
                        result.className = `result ${isPhishing ? 'phishing' : 'legitimate'}`;
                        resultContent.innerHTML = `
                            <h3>${isPhishing ? '⚠️ Phishing Detected' : '✅ Legitimate URL'}</h3>
                            <p><strong>Prediction:</strong> ${data.prediction_label}</p>
                            <div class="details">
                                <p><strong>Confidence:</strong> ${confidence}%</p>
                                <p><strong>Processing Time:</strong> ${(data.processing_time * 1000).toFixed(0)}ms</p>
                                ${data.probabilities ? `
                                    <p><strong>Probabilities:</strong></p>
                                    <ul>
                                        <li>Legitimate: ${(data.probabilities.legitimate * 100).toFixed(1)}%</li>
                                        <li>Phishing: ${(data.probabilities.phishing * 100).toFixed(1)}%</li>
                                    </ul>
                                ` : ''}
                            </div>
                        `;
                    } else {
                        // Error
                        result.className = 'result error';
                        resultContent.innerHTML = `
                            <h3>❌ Error</h3>
                            <p>${data.detail || 'An error occurred while analyzing the URL'}</p>
                        `;
                    }
                    
                } catch (error) {
                    result.className = 'result error';
                    resultContent.innerHTML = `
                        <h3>❌ Network Error</h3>
                        <p>Failed to connect to the server. Please try again.</p>
                    `;
                }
                
                // Hide loading state and show result
                loading.style.display = 'none';
                result.style.display = 'block';
                submitBtn.disabled = false;
                submitBtn.textContent = 'Analyze URL';
            });
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=f"HTTP {exc.status_code}",
            detail=exc.detail
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    logger.error(traceback.format_exc())
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            detail="An unexpected error occurred"
        ).dict()
    )

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
