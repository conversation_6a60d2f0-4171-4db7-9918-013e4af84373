#!/usr/bin/env python3
"""
Test script for the Phishing Detection API

This script tests all components of the phishing detection system:
1. Feature extraction
2. Preprocessing
3. Model prediction
4. API endpoints
"""

import requests
import pandas as pd
import numpy as np
import time
import json
from feature_extractor import URLFeatureExtractor
from preprocessor import PhishingPreprocessor

def test_feature_extraction():
    """Test the feature extraction module."""
    print("=" * 60)
    print("🧪 TESTING FEATURE EXTRACTION")
    print("=" * 60)
    
    test_urls = [
        "https://www.google.com",
        "https://github.com",
        "http://example.com"
    ]
    
    extractor = URLFeatureExtractor()
    
    for url in test_urls:
        print(f"\n🔍 Testing URL: {url}")
        try:
            start_time = time.time()
            features = extractor.extract_features(url)
            extraction_time = time.time() - start_time
            
            print(f"✅ Features extracted successfully")
            print(f"📊 Shape: {features.shape}")
            print(f"⏱️ Time: {extraction_time:.2f}s")
            print(f"🔢 Sample features: {dict(list(features.iloc[0].items())[:5])}")
            
            # Validate feature count and names
            expected_features = [
                'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
                'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
                'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
                'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
                'HasCopyrightInfoKey', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
                'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
                'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
            ]
            
            if list(features.columns) == expected_features:
                print("✅ Feature names and order are correct")
            else:
                print("❌ Feature names or order mismatch")
                print(f"Expected: {expected_features}")
                print(f"Got: {list(features.columns)}")
            
        except Exception as e:
            print(f"❌ Error: {e}")

def test_preprocessing():
    """Test the preprocessing module."""
    print("\n" + "=" * 60)
    print("🧪 TESTING PREPROCESSING")
    print("=" * 60)
    
    try:
        # Initialize preprocessor
        preprocessor = PhishingPreprocessor()
        
        if not preprocessor.is_loaded:
            print("❌ Failed to load model package")
            return
        
        print("✅ Preprocessor loaded successfully")
        print(f"📊 Expected features: {len(preprocessor.selected_features)}")
        
        # Create test data
        feature_names = [
            'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
            'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
            'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
            'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
            'HasCopyrightInfoKey ', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
            'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
            'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
        ]
        
        # Create test data with realistic values
        test_data = pd.DataFrame({
            'LengthOfURL': [25],
            'URLComplexity': [5.2],
            'TLD': ['com'],
            'LetterCntInURL': [18],
            'URLLetterRatio': [0.72],
            'DigitCntInURL': [0],
            'URLDigitRatio': [0.0],
            'OtherSpclCharCntInURL': [2],
            'HavingPath': [1],
            'PathLength': [8],
            'HasSSL': [1],
            'LineOfCode': [150],
            'LongestLineLength': [80],
            'HasFavicon': [1],
            'HasRobotsBlocked': [0],
            'IsSelfRedirects': [0],
            'HasDescription': [1],
            'HasSubmitButton': [0],
            'HasCopyrightInfoKey ': [1],
            'CntImages': [5],
            'CntFilesCSS': [2],
            'CntFilesJS': [3],
            'CntSelfHRef': [10],
            'CntEmptyRef': [0],
            'CntExternalRef': [2],
            'CntIFrame': [0],
            'UniqueFeatureCnt': [25],
            'ShannonEntropy': [4.2],
            'KolmogorovComplexity': [0.6],
            'LikelinessIndex': [0.8]
        })
        
        print(f"🔍 Test data shape: {test_data.shape}")
        
        # Test preprocessing
        start_time = time.time()
        processed = preprocessor.preprocess(test_data)
        preprocessing_time = time.time() - start_time
        
        print(f"✅ Preprocessing successful")
        print(f"📊 Processed shape: {processed.shape}")
        print(f"⏱️ Time: {preprocessing_time:.3f}s")
        
        # Test prediction
        start_time = time.time()
        predictions, probabilities = preprocessor.predict(test_data)
        prediction_time = time.time() - start_time
        
        print(f"✅ Prediction successful")
        print(f"🎯 Prediction: {predictions[0]} ({'Phishing' if predictions[0] == 1 else 'Legitimate'})")
        if probabilities is not None:
            print(f"📊 Probabilities: Legitimate={probabilities[0][0]:.3f}, Phishing={probabilities[0][1]:.3f}")
        print(f"⏱️ Time: {prediction_time:.3f}s")
        
    except Exception as e:
        print(f"❌ Error in preprocessing: {e}")
        import traceback
        traceback.print_exc()

def test_api_endpoints():
    """Test the API endpoints."""
    print("\n" + "=" * 60)
    print("🧪 TESTING API ENDPOINTS")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Test health endpoint
    print("\n🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Health check passed")
            print(f"📊 Status: {health_data.get('status')}")
            print(f"🔧 Components: {health_data.get('components')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Make sure it's running on localhost:8000")
        return
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return
    
    # Test prediction endpoint
    print("\n🔍 Testing prediction endpoint...")
    test_urls = [
        "https://www.google.com",
        "https://github.com",
        "http://example.com"
    ]
    
    for url in test_urls:
        print(f"\n📍 Testing URL: {url}")
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/predict",
                json={"url": url},
                timeout=30
            )
            request_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Prediction successful")
                print(f"🎯 Result: {data['prediction_label']}")
                print(f"📊 Confidence: {data.get('confidence', 'N/A')}")
                print(f"⏱️ Processing time: {data.get('processing_time', 'N/A')}s")
                print(f"🌐 Request time: {request_time:.2f}s")
            else:
                print(f"❌ Prediction failed: {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    # Test batch endpoint
    print("\n🔍 Testing batch prediction endpoint...")
    try:
        batch_urls = ["https://www.google.com", "https://github.com"]
        start_time = time.time()
        response = requests.post(
            f"{base_url}/predict/batch",
            json=batch_urls,
            timeout=60
        )
        request_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Batch prediction successful")
            print(f"📊 Total URLs: {data.get('total')}")
            print(f"✅ Processed: {data.get('processed')}")
            print(f"⏱️ Request time: {request_time:.2f}s")
        else:
            print(f"❌ Batch prediction failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Batch prediction error: {e}")

def test_end_to_end():
    """Test the complete end-to-end pipeline."""
    print("\n" + "=" * 60)
    print("🧪 TESTING END-TO-END PIPELINE")
    print("=" * 60)
    
    test_url = "https://www.google.com"
    print(f"🔍 Testing complete pipeline with: {test_url}")
    
    try:
        # Step 1: Feature extraction
        print("\n1️⃣ Feature extraction...")
        extractor = URLFeatureExtractor()
        features = extractor.extract_features(test_url)
        print(f"✅ Extracted {features.shape[1]} features")
        
        # Step 2: Preprocessing
        print("\n2️⃣ Preprocessing...")
        preprocessor = PhishingPreprocessor()
        processed = preprocessor.preprocess(features)
        print(f"✅ Preprocessed to shape: {processed.shape}")
        
        # Step 3: Prediction
        print("\n3️⃣ Prediction...")
        predictions, probabilities = preprocessor.predict(features)
        result = "Phishing" if predictions[0] == 1 else "Legitimate"
        confidence = max(probabilities[0]) if probabilities is not None else None
        
        print(f"✅ Final prediction: {result}")
        print(f"📊 Confidence: {confidence:.3f if confidence is not None else 'N/A'}")
        
        print("\n🎉 End-to-end test completed successfully!")
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests."""
    print("🚀 PHISHING DETECTION API - COMPREHENSIVE TESTING")
    print("=" * 80)
    
    # Test individual components
    test_feature_extraction()
    test_preprocessing()
    
    # Test end-to-end pipeline
    test_end_to_end()
    
    # Test API endpoints (requires running server)
    print("\n" + "=" * 60)
    print("📡 API ENDPOINT TESTING")
    print("=" * 60)
    print("ℹ️ Make sure the API server is running: python app.py")
    
    user_input = input("\nDo you want to test API endpoints? (y/n): ").lower().strip()
    if user_input == 'y':
        test_api_endpoints()
    else:
        print("⏭️ Skipping API endpoint tests")
    
    print("\n" + "=" * 80)
    print("🎉 TESTING COMPLETED")
    print("=" * 80)

if __name__ == "__main__":
    main()
