#!/usr/bin/env python3
"""
Data Preprocessing Pipeline for Phishing Detection

This module implements the exact preprocessing pipeline used during model training:
1. Missing value imputation (median for numeric, most frequent for categorical)
2. Target encoding for categorical features (TLD)
3. Standard scaling for all features

The pipeline matches the preprocessing steps from stacking.py exactly.
"""

import pandas as pd
import numpy as np
import joblib
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler
from category_encoders import TargetEncoder
import warnings

warnings.filterwarnings('ignore')


class PhishingPreprocessor:
    """
    Preprocessing pipeline that matches the exact steps from training.
    
    This preprocessor applies only the 3 core preprocessing steps:
    1. Missing value imputation
    2. Target encoding for TLD
    3. Standard scaling
    
    It does NOT include feature selection as the 30 features are already selected.
    """
    
    def __init__(self, model_package_path='stacking_ensemble.joblib'):
        """
        Initialize preprocessor with saved model components.
        
        Args:
            model_package_path (str): Path to the saved model package
        """
        self.model_package_path = model_package_path
        self.model_package = None
        self.num_imputer = None
        self.cat_imputer = None
        self.target_encoder = None
        self.scaler = None
        self.feature_selector = None
        self.categorical_cols = []
        self.numerical_cols = []
        self.selected_features = []
        self.is_loaded = False
        
        # Load the model package
        self._load_model_package()
    
    def _load_model_package(self):
        """Load the saved model package and extract preprocessing components."""
        try:
            self.model_package = joblib.load(self.model_package_path)
            
            # Extract preprocessing components
            self.num_imputer = self.model_package.get('num_imputer')
            self.cat_imputer = self.model_package.get('cat_imputer')
            self.target_encoder = self.model_package.get('target_encoder')
            self.scaler = self.model_package.get('scaler')
            self.feature_selector = self.model_package.get('feature_selector')
            
            # Extract feature information
            self.categorical_cols = self.model_package.get('categorical_cols', [])
            self.numerical_cols = self.model_package.get('numerical_cols', [])
            self.selected_features = self.model_package.get('selected_features', [])
            
            self.is_loaded = True
            print(f"✅ Model package loaded successfully")
            print(f"📊 Selected features: {len(self.selected_features)}")
            print(f"📊 Categorical columns: {self.categorical_cols}")
            
        except Exception as e:
            print(f"❌ Error loading model package: {e}")
            self.is_loaded = False
    
    def preprocess(self, X):
        """
        Apply the complete preprocessing pipeline to input features.
        
        Args:
            X (pd.DataFrame): Input features with 30 columns matching selected_features
            
        Returns:
            np.ndarray: Preprocessed features ready for model prediction
        """
        if not self.is_loaded:
            raise ValueError("Model package not loaded. Cannot preprocess data.")
        
        try:
            # Ensure input has the correct features
            if list(X.columns) != self.selected_features:
                print(f"⚠️ Warning: Input features don't match expected features")
                print(f"Expected: {self.selected_features}")
                print(f"Received: {list(X.columns)}")
                
                # Reorder columns to match expected features
                X = X.reindex(columns=self.selected_features, fill_value=0)
            
            X_processed = X.copy()
            
            # Step 1: Handle Missing Values
            # Apply imputation to numerical features
            numerical_features_in_data = [col for col in self.numerical_cols if col in X_processed.columns]
            if numerical_features_in_data and self.num_imputer:
                X_processed[numerical_features_in_data] = self.num_imputer.transform(X_processed[numerical_features_in_data])
            
            # Apply imputation to categorical features
            categorical_features_in_data = [col for col in self.categorical_cols if col in X_processed.columns]
            if categorical_features_in_data and self.cat_imputer:
                X_processed[categorical_features_in_data] = self.cat_imputer.transform(X_processed[categorical_features_in_data])
            
            # Step 2: Target Encoding for Categorical Features
            if categorical_features_in_data and self.target_encoder:
                X_processed = self.target_encoder.transform(X_processed)
            
            # Step 3: Standard Scaling
            # Create the full feature set expected by the scaler
            X_full = self._create_full_feature_set(X_processed)
            
            # Apply scaling
            if self.scaler:
                X_scaled = self.scaler.transform(X_full)
            else:
                X_scaled = X_full.values
            
            # Step 4: Feature Selection to get final 30 features
            if self.feature_selector:
                X_final = self.feature_selector.transform(X_scaled)
            else:
                X_final = X_scaled
            
            return X_final
            
        except Exception as e:
            print(f"❌ Error in preprocessing: {e}")
            # Fallback: try simple preprocessing
            return self._simple_preprocess(X)
    
    def _create_full_feature_set(self, X_selected):
        """
        Create the full feature set expected by the scaler.
        
        The scaler was trained on features after variance selection (56 features),
        but we only have the 30 selected features. We need to reconstruct the
        full feature set with default values for missing features.
        """
        try:
            # Get the feature names after variance selection
            feature_names_after_var = self.model_package.get('feature_names_after_var', [])
            
            if not feature_names_after_var:
                # If we don't have the full feature list, use the selected features directly
                return X_selected
            
            # Create DataFrame with all features after variance selection
            X_full = pd.DataFrame(index=X_selected.index)
            
            # Add all features in the correct order
            for feature in feature_names_after_var:
                if feature in X_selected.columns:
                    # Use the actual value from our extracted features
                    X_full[feature] = X_selected[feature]
                elif feature in self.categorical_cols:
                    # Default value for categorical features
                    X_full[feature] = 'unknown'
                else:
                    # Default value for numerical features (use median from training)
                    X_full[feature] = 0
            
            return X_full
            
        except Exception as e:
            print(f"⚠️ Warning: Could not create full feature set: {e}")
            return X_selected
    
    def _simple_preprocess(self, X):
        """
        Simple fallback preprocessing when the full pipeline fails.
        
        Args:
            X (pd.DataFrame): Input features
            
        Returns:
            np.ndarray: Simply processed features
        """
        try:
            X_processed = X.copy()
            
            # Handle TLD encoding manually if present
            if 'TLD' in X_processed.columns:
                # Simple TLD encoding
                tld_mapping = {
                    'com': 1, 'org': 2, 'net': 3, 'edu': 4, 'gov': 5,
                    'mil': 6, 'int': 7, 'co': 8, 'uk': 9, 'de': 10,
                    'unknown': 0
                }
                X_processed['TLD'] = X_processed['TLD'].map(tld_mapping).fillna(0)
            
            # Fill missing values
            X_processed = X_processed.fillna(0)
            
            # Convert to numpy array
            return X_processed.values
            
        except Exception as e:
            print(f"❌ Error in simple preprocessing: {e}")
            # Ultimate fallback: return zeros
            return np.zeros((X.shape[0], len(self.selected_features)))
    
    def get_model(self):
        """Get the trained stacking classifier."""
        if not self.is_loaded:
            raise ValueError("Model package not loaded.")
        return self.model_package.get('stacking_classifier')
    
    def predict(self, X):
        """
        Preprocess features and make prediction.
        
        Args:
            X (pd.DataFrame): Input features
            
        Returns:
            tuple: (predictions, probabilities)
        """
        # Preprocess the features
        X_processed = self.preprocess(X)
        
        # Get the model
        model = self.get_model()
        
        # Make predictions
        predictions = model.predict(X_processed)
        
        # Get prediction probabilities if available
        try:
            probabilities = model.predict_proba(X_processed)
        except:
            probabilities = None
        
        return predictions, probabilities


def preprocess_features(features_df, model_package_path='stacking_ensemble.joblib'):
    """
    Convenience function to preprocess features using the trained pipeline.
    
    Args:
        features_df (pd.DataFrame): DataFrame with 30 extracted features
        model_package_path (str): Path to the model package
        
    Returns:
        np.ndarray: Preprocessed features ready for prediction
    """
    preprocessor = PhishingPreprocessor(model_package_path)
    return preprocessor.preprocess(features_df)


if __name__ == "__main__":
    # Test the preprocessor
    print("Testing Phishing Preprocessor...")
    
    try:
        preprocessor = PhishingPreprocessor()
        
        # Create dummy features for testing
        feature_names = [
            'LengthOfURL', 'URLComplexity', 'TLD', 'LetterCntInURL', 'URLLetterRatio',
            'DigitCntInURL', 'URLDigitRatio', 'OtherSpclCharCntInURL', 'HavingPath',
            'PathLength', 'HasSSL', 'LineOfCode', 'LongestLineLength', 'HasFavicon',
            'HasRobotsBlocked', 'IsSelfRedirects', 'HasDescription', 'HasSubmitButton',
            'HasCopyrightInfoKey', 'CntImages', 'CntFilesCSS', 'CntFilesJS',
            'CntSelfHRef', 'CntEmptyRef', 'CntExternalRef', 'CntIFrame',
            'UniqueFeatureCnt', 'ShannonEntropy', 'KolmogorovComplexity', 'LikelinessIndex'
        ]
        
        # Create test data
        test_data = pd.DataFrame({
            feature: [np.random.random() if feature != 'TLD' else 'com' for _ in range(1)]
            for feature in feature_names
        })
        
        print(f"Test data shape: {test_data.shape}")
        
        # Test preprocessing
        processed = preprocessor.preprocess(test_data)
        print(f"Processed data shape: {processed.shape}")
        
        # Test prediction
        predictions, probabilities = preprocessor.predict(test_data)
        print(f"Predictions: {predictions}")
        print(f"Probabilities shape: {probabilities.shape if probabilities is not None else None}")
        
    except Exception as e:
        print(f"Error testing preprocessor: {e}")
